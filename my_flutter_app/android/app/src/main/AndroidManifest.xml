<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Permissions for UPI functionality -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:label="my_flutter_app"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>

        <!-- UPI Apps package names for detection and launching -->
        <!-- Google Pay (GPay) -->
        <package android:name="com.google.android.apps.nbu.paisa.user" />
        <!-- PhonePe -->
        <package android:name="com.phonepe.app" />
        <!-- Paytm -->
        <package android:name="net.one97.paytm" />
        <!-- BHIM -->
        <package android:name="in.org.npci.upiapp" />
        <!-- Amazon Pay -->
        <package android:name="in.amazon.mShop.android.shopping" />
        <!-- WhatsApp -->
        <package android:name="com.whatsapp" />
        <!-- WhatsApp Business -->
        <package android:name="com.whatsapp.w4b" />
        <!-- Mobikwik -->
        <package android:name="com.mobikwik_new" />
        <!-- Freecharge -->
        <package android:name="com.freecharge.android" />
        <!-- CRED -->
        <package android:name="com.dreamplug.androidapp" />
        <!-- Airtel Thanks -->
        <package android:name="com.myairtelapp" />
        <!-- ICICI iMobile -->
        <package android:name="com.csam.icici.bank.imobile" />
        <!-- SBI Pay -->
        <package android:name="com.sbi.upi" />
        <!-- HDFC PayZapp -->
        <package android:name="com.snapwork.hdfc" />
        <!-- Axis Pay -->
        <package android:name="com.axis.mobile" />
        <!-- Truecaller -->
        <package android:name="com.truecaller" />
        <!-- Mi Pay -->
        <package android:name="com.mipay.wallet.in" />
        <!-- LazyPay -->
        <package android:name="me.lazypay.android" />
        <!-- FamPay -->
        <package android:name="in.fampay.app" />
        <!-- YONO SBI -->
        <package android:name="com.sbi.lotusintouch" />
        <!-- Kotak 811 & Mobile Banking -->
        <package android:name="com.msf.kbank.mobile" />
        <!-- HDFC Bank MobileBanking -->
        <package android:name="com.snapwork.hdfc" />
        <!-- ICICI Bank iMobile Pay -->
        <package android:name="com.csam.icici.bank.imobile" />
        <!-- Axis Mobile -->
        <package android:name="com.axis.mobile" />
        <!-- Bank of Baroda M-Connect Plus -->
        <package android:name="com.baroda.mconnectplus" />
        <!-- Canara Bank Mobile Banking -->
        <package android:name="com.infrasofttech.CanaraBank" />
        <!-- Union Bank UMobile -->
        <package android:name="com.unionbank.ebanking" />
        <!-- PNB ONE -->
        <package android:name="com.fss.pnbone" />
        <!-- Central Bank of India M-Passbook -->
        <package android:name="com.centralbank.cbimpassbook" />
        <!-- Indian Bank IndMobile -->
        <package android:name="com.indianbank.indmobile" />
        <!-- Bank of India Star Mobile -->
        <package android:name="com.boi.mobile" />
        <!-- UCO Bank UCO mBanking -->
        <package android:name="com.uco.ucobank" />
        <!-- Indian Overseas Bank IOB Mobile -->
        <package android:name="com.iob.mobile" />
        <!-- Punjab & Sind Bank PSB M-Banking -->
        <package android:name="com.psb.mbanking" />

        <!-- UPI Intent for general UPI apps -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="upi" />
        </intent>
    </queries>
</manifest>
