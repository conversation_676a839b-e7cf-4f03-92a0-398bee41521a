<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application
        android:label="my_flutter_app"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>

        <!-- UPI Apps package names for detection and launching -->
        <!-- Google Pay -->
        <package android:name="com.google.android.apps.nbu.paisa.user" />
        <!-- PhonePe -->
        <package android:name="com.phonepe.app" />
        <!-- Paytm -->
        <package android:name="net.one97.paytm" />
        <!-- BHIM -->
        <package android:name="in.org.npci.upiapp" />
        <!-- Amazon Pay -->
        <package android:name="in.amazon.mShop.android.shopping" />
        <!-- WhatsApp -->
        <package android:name="com.whatsapp" />
        <!-- Mobikwik -->
        <package android:name="com.mobikwik_new" />
        <!-- Freecharge -->
        <package android:name="com.freecharge.android" />
        <!-- CRED -->
        <package android:name="com.dreamplug.androidapp" />
        <!-- Airtel Thanks -->
        <package android:name="com.myairtelapp" />
        <!-- ICICI iMobile -->
        <package android:name="com.csam.icici.bank.imobile" />
        <!-- SBI Pay -->
        <package android:name="com.sbi.upi" />
        <!-- HDFC PayZapp -->
        <package android:name="com.snapwork.hdfc" />
        <!-- Axis Pay -->
        <package android:name="com.axis.mobile" />
        <!-- Truecaller -->
        <package android:name="com.truecaller" />
        <!-- Mi Pay -->
        <package android:name="com.mipay.wallet.in" />
        <!-- LazyPay -->
        <package android:name="me.lazypay.android" />
        <!-- FamPay -->
        <package android:name="in.fampay.app" />

        <!-- UPI Intent for general UPI apps -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="upi" />
        </intent>
    </queries>
</manifest>
