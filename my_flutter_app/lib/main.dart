import 'package:flutter/material.dart';
import 'package:upi_pay/upi_pay.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'UPI Payment App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: UPIPaymentScreen(),
    );
  }
}

class UPIPaymentScreen extends StatefulWidget {
  @override
  _UPIPaymentScreenState createState() => _UPIPaymentScreenState();
}

class _UPIPaymentScreenState extends State<UPIPaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _upiIdController = TextEditingController();
  final _amountController = TextEditingController();
  final _nameController = TextEditingController();
  final _remarkController = TextEditingController();

  // Create UpiPay instance
  final _upiPay = UpiPay();

  List<ApplicationMeta>? _apps;
  String _upiAddrError = '';

  @override
  void initState() {
    super.initState();
    _loadUPIApps();
  }

  // Load installed UPI apps
  Future<void> _loadUPIApps() async {
    try {
      final apps = await _upiPay.getInstalledUpiApplications();
      setState(() {
        _apps = apps;
      });
    } catch (e) {
      print('Error loading UPI apps: $e');
    }
  }

  // Validate UPI ID format
  bool _validateUPIId(String upiId) {
    final regex = RegExp(r'^[\\w\\.\\-]+@[\\w\\-]+$');
    return regex.hasMatch(upiId);
  }

  // Initiate UPI payment
  Future<void> _initiateTransaction(ApplicationMeta app) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final String upiId = _upiIdController.text.trim();
    final String amount = _amountController.text.trim();
    final String name = _nameController.text.trim();
    final String remark = _remarkController.text.trim();

    try {
      final response = await _upiPay.initiateTransaction(
        amount: amount,
        app: app.upiApplication,
        receiverName: name,
        receiverUpiAddress: upiId,
        transactionRef: 'TXN${DateTime.now().millisecondsSinceEpoch}',
        transactionNote: remark.isEmpty ? 'Payment' : remark,
      );

      _handleTransactionResponse(response);
    } catch (e) {
      _showSnackBar('Transaction failed: $e', isError: true);
    }
  }

  // Handle transaction response
  void _handleTransactionResponse(UpiTransactionResponse response) {
    String message;
    bool isError = false;

    switch (response.status) {
      case UpiTransactionStatus.success:
        message = 'Payment successful! Transaction ID: ${response.txnId}';
        _clearForm();
        break;
      case UpiTransactionStatus.submitted:
        message = 'Payment submitted. Please check your UPI app.';
        break;
      case UpiTransactionStatus.failure:
        message = 'Payment failed. Please try again.';
        isError = true;
        break;
      default:
        message = 'Unknown status: ${response.status}';
        isError = true;
    }

    _showSnackBar(message, isError: isError);
  }

  // Show snackbar message
  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  // Clear form fields
  void _clearForm() {
    _upiIdController.clear();
    _amountController.clear();
    _nameController.clear();
    _remarkController.clear();
  }

  // Show UPI apps bottom sheet
  void _showAppSelection() {
    if (_apps == null || _apps!.isEmpty) {
      _showSnackBar('No UPI apps found. Please install a UPI app.',
          isError: true);
      return;
    }

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select UPI App',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            ..._apps!.map((app) => ListTile(
                  leading: app.iconImage(48),
                  title: Text(app.upiApplication.getAppName()),
                  onTap: () {
                    Navigator.pop(context);
                    _initiateTransaction(app);
                  },
                )),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _upiIdController.dispose();
    _amountController.dispose();
    _nameController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('UPI Payment'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // UPI ID Field
              TextFormField(
                controller: _upiIdController,
                decoration: InputDecoration(
                  labelText: 'UPI ID',
                  hintText: 'example@upi',
                  prefixIcon: Icon(Icons.account_balance_wallet),
                  border: OutlineInputBorder(),
                  errorText: _upiAddrError.isEmpty ? null : _upiAddrError,
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter UPI ID';
                  }
                  if (!_validateUPIId(value)) {
                    return 'Invalid UPI ID format';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    _upiAddrError = '';
                  });
                },
              ),
              SizedBox(height: 16),

              // Recipient Name Field
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'Recipient Name',
                  hintText: 'Enter recipient name',
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter recipient name';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Amount Field
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText: 'Amount',
                  hintText: 'Enter amount',
                  prefixIcon: Icon(Icons.currency_rupee),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter amount';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  if (amount > 100000) {
                    return 'Amount cannot exceed ₹1,00,000';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),

              // Remark Field (Optional)
              TextFormField(
                controller: _remarkController,
                decoration: InputDecoration(
                  labelText: 'Remark (Optional)',
                  hintText: 'Enter payment remark',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              SizedBox(height: 24),

              // Pay Button
              ElevatedButton(
                onPressed: _showAppSelection,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.payment),
                    SizedBox(width: 8),
                    Text(
                      'Pay Now',
                      style: TextStyle(fontSize: 18),
                    ),
                  ],
                ),
              ),

              // Info Card
              SizedBox(height: 24),
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue),
                          SizedBox(width: 8),
                          Text(
                            'Payment Information',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade900,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        '• Ensure the UPI ID is correct\\n'
                        '• Maximum transaction limit: ₹1,00,000\\n'
                        '• Payment will be processed through your selected UPI app',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
